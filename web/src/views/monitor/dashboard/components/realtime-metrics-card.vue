<script setup lang="ts">
import { NCard, NGrid, NGridItem, NTag } from 'naive-ui';
import { Icon } from '@iconify/vue';
import MetricItem from './metric-item.vue';
import { useMetrics } from '../composables/useMetrics';

interface Props {
  healthData?: Api.Monitor.AppHealthData | null;
  businessData?: Api.Monitor.BusinessStatsData | null;
  performanceData?: Api.Monitor.PerformanceData | null;
  systemData?: Api.Monitor.SystemOverviewData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  healthData: null,
  businessData: null,
  performanceData: null,
  systemData: null,
  loading: false
});

// 使用指标计算 composable
const { coreMetrics } = useMetrics(props);
</script>

<template>
  <NCard class="realtime-metrics-card">
    <template #header>
      <div class="card-header">
        <div class="header-content">
          <Icon icon="mdi:chart-line" class="header-icon" />
          <span class="header-title">实时监控概览</span>
        </div>
        <NTag type="success" size="small" class="status-tag">
          <Icon icon="mdi:circle" class="status-icon" />
          实时更新
        </NTag>
      </div>
    </template>

    <NGrid :cols="24" :x-gap="16" :y-gap="16" class="metrics-grid">
      <NGridItem
        v-for="metric in coreMetrics"
        :key="metric.key"
        :span="24"
        :sm-span="12"
        :lg-span="8"
        :xl-span="6"
      >
        <MetricItem
          :icon="metric.icon"
          :label="metric.label"
          :value="metric.value"
          :type="metric.type"
          :status="metric.status"
          :progress="metric.progress"
          :extra="metric.extra"
          :icon-color="metric.iconColor"
        />
      </NGridItem>
    </NGrid>
  </NCard>
</template>

<style scoped>
.realtime-metrics-card {
  background: white;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 20px;
  color: #1890ff;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-icon {
  font-size: 8px;
  animation: pulse 2s infinite;
}

.metrics-grid {
  margin-top: 4px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .metrics-grid :deep(.n-grid-item) {
    --n-grid-item-span: 8 !important;
  }
}

@media (max-width: 768px) {
  .metrics-grid :deep(.n-grid-item) {
    --n-grid-item-span: 12 !important;
  }
}

@media (max-width: 576px) {
  .metrics-grid :deep(.n-grid-item) {
    --n-grid-item-span: 24 !important;
  }
}
</style>
