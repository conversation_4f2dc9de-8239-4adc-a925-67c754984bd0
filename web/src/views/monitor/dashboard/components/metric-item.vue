<script setup lang="ts">
import { computed } from 'vue';
import { NStatistic, NTag, NProgress } from 'naive-ui';
import { Icon } from '@iconify/vue';

interface Props {
  /** 图标名称 */
  icon: string;
  /** 指标标签 */
  label: string;
  /** 指标值 */
  value: string | number;
  /** 指标类型 */
  type?: 'number' | 'percentage' | 'status' | 'time';
  /** 状态类型 */
  status?: 'success' | 'warning' | 'error' | 'info';
  /** 进度值（用于百分比类型） */
  progress?: number;
  /** 额外信息 */
  extra?: string;
  /** 图标颜色 */
  iconColor?: string;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'number',
  status: 'info',
  progress: 0,
  extra: '',
  iconColor: '#1890ff'
});

// 计算显示值
const displayValue = computed(() => {
  if (props.type === 'percentage') {
    return `${Number(props.value).toFixed(1)}%`;
  }
  if (props.type === 'time') {
    return `${Number(props.value).toFixed(2)}s`;
  }
  return props.value;
});

// 计算状态颜色
const statusColor = computed(() => {
  switch (props.status) {
    case 'success':
      return '#52c41a';
    case 'warning':
      return '#faad14';
    case 'error':
      return '#ff4d4f';
    default:
      return '#1890ff';
  }
});

// 计算标签类型
const tagType = computed(() => {
  switch (props.status) {
    case 'success':
      return 'success';
    case 'warning':
      return 'warning';
    case 'error':
      return 'error';
    default:
      return 'info';
  }
});
</script>

<template>
  <div class="metric-item">
    <div class="metric-header">
      <Icon :icon="icon" class="metric-icon" :style="{ color: iconColor }" />
      <span class="metric-label">{{ label }}</span>
    </div>
    
    <div class="metric-content">
      <!-- 数值类型 -->
      <div v-if="type === 'number'" class="metric-value">
        <span class="value-text">{{ displayValue }}</span>
        <span v-if="extra" class="extra-text">{{ extra }}</span>
      </div>
      
      <!-- 百分比类型 -->
      <div v-else-if="type === 'percentage'" class="metric-value">
        <span class="value-text">{{ displayValue }}</span>
        <NProgress
          v-if="progress > 0"
          :percentage="progress"
          :color="statusColor"
          :show-indicator="false"
          class="progress-bar"
        />
      </div>
      
      <!-- 状态类型 -->
      <div v-else-if="type === 'status'" class="metric-value">
        <NTag :type="tagType" size="large">
          {{ displayValue }}
        </NTag>
      </div>
      
      <!-- 时间类型 -->
      <div v-else-if="type === 'time'" class="metric-value">
        <span class="value-text">{{ displayValue }}</span>
        <NTag :type="tagType" size="small" class="status-tag">
          {{ extra }}
        </NTag>
      </div>
      
      <!-- 默认类型 -->
      <div v-else class="metric-value">
        <span class="value-text">{{ displayValue }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.metric-item {
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.metric-item:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.metric-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.metric-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.metric-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.metric-value {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.value-text {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  line-height: 1;
}

.extra-text {
  font-size: 12px;
  color: #999;
}

.progress-bar {
  width: 100%;
}

.status-tag {
  align-self: flex-start;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .metric-item {
    padding: 16px;
  }
  
  .value-text {
    font-size: 20px;
  }
  
  .metric-icon {
    font-size: 18px;
  }
}
</style>
