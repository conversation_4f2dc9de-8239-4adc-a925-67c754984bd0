import { computed, toRefs, Ref } from 'vue';
import {
  normalizePercentage,
  getStatusColor,
  getSuccessRateStatus,
  getResponseTimeStatus,
  getUsageStatus,
  getErrorRateStatus
} from '../utils/formatters';

interface MetricData {
  healthData?: Api.Monitor.AppHealthData | null;
  businessData?: Api.Monitor.BusinessStatsData | null;
  performanceData?: Api.Monitor.PerformanceData | null;
  systemData?: Api.Monitor.SystemOverviewData | null;
}

/**
 * 监控指标计算 Composable
 * 提供类型安全的指标计算和数据访问
 */
export function useMetrics(data: MetricData) {
  // 确保响应式
  const { healthData, businessData, performanceData, systemData } = toRefs(data) as {
    healthData: Ref<Api.Monitor.AppHealthData | null | undefined>;
    businessData: Ref<Api.Monitor.BusinessStatsData | null | undefined>;
    performanceData: Ref<Api.Monitor.PerformanceData | null | undefined>;
    systemData: Ref<Api.Monitor.SystemOverviewData | null | undefined>;
  };
  // 系统状态指标
  const systemStatus = computed(() => {
    const status = healthData.value?.app_info?.status || 'unknown';
    const statusMap = {
      healthy: { text: '正常', status: 'success' as const },
      warning: { text: '警告', status: 'warning' as const },
      error: { text: '异常', status: 'error' as const },
      unknown: { text: '未知', status: 'info' as const }
    };
    return statusMap[status as keyof typeof statusMap] || statusMap.unknown;
  });

  // 任务概览指标
  const taskOverview = computed(() => {
    const tasks = businessData.value?.strm_tasks;
    if (!tasks) {
      return {
        total: 0,
        running: 0,
        successRate: 0,
        extra: '运行中: 0'
      };
    }

    return {
      total: tasks.total || 0,
      running: tasks.running || 0,
      successRate: normalizePercentage(tasks.success_rate),
      extra: `运行中: ${tasks.running || 0}`
    };
  });

  // 性能指标
  const performanceMetrics = computed(() => {
    const perf = performanceData.value?.api_performance;
    if (!perf) {
      return {
        avgResponseTime: 0,
        status: 'info' as const,
        statusText: '未知'
      };
    }

    const responseTime = perf.avg_response_time || 0;
    const responseTimeStatus = getResponseTimeStatus(responseTime);

    return {
      avgResponseTime: responseTime,
      status: responseTimeStatus.type,
      statusText: responseTimeStatus.text
    };
  });

  // 资源使用率指标
  const resourceUsage = computed(() => {
    const system = systemData.value;
    if (!system?.available) {
      return {
        usage: 0,
        status: 'info' as const,
        description: 'CPU: 0% | 内存: 0%'
      };
    }

    const cpuUsage = system.cpu?.percent || 0;
    const memoryUsage = system.memory?.percent || 0;
    const avgUsage = (cpuUsage + memoryUsage) / 2;
    const usageStatus = getUsageStatus(avgUsage);

    return {
      usage: avgUsage,
      status: usageStatus.type,
      description: `CPU: ${cpuUsage.toFixed(1)}% | 内存: ${memoryUsage.toFixed(1)}%`
    };
  });

  // 错误率指标
  const errorRate = computed(() => {
    const perf = performanceData.value?.api_performance;
    if (!perf) {
      return {
        rate: 0,
        status: 'success' as const,
        statusText: '正常'
      };
    }

    const rate = normalizePercentage(perf.error_rate);
    const errorRateStatus = getErrorRateStatus(rate);

    return {
      rate,
      status: errorRateStatus.type,
      statusText: errorRateStatus.text
    };
  });

  // 获取所有核心指标
  const coreMetrics = computed(() => [
    {
      key: 'system',
      icon: 'mdi:server',
      label: '系统状态',
      value: systemStatus.value.text,
      type: 'status' as const,
      status: systemStatus.value.status,
      iconColor: getStatusColor(systemStatus.value.status)
    },
    {
      key: 'tasks',
      icon: 'mdi:format-list-checks',
      label: '任务总数',
      value: taskOverview.value.total,
      type: 'number' as const,
      status: 'info' as const,
      extra: taskOverview.value.extra,
      iconColor: '#1890ff'
    },
    {
      key: 'success',
      icon: 'mdi:check-circle',
      label: '成功率',
      value: taskOverview.value.successRate,
      type: 'percentage' as const,
      status: getSuccessRateStatus(taskOverview.value.successRate),
      progress: taskOverview.value.successRate,
      iconColor: '#52c41a'
    },
    {
      key: 'performance',
      icon: 'mdi:speedometer',
      label: '平均响应时间',
      value: performanceMetrics.value.avgResponseTime,
      type: 'time' as const,
      status: performanceMetrics.value.status,
      extra: performanceMetrics.value.statusText,
      iconColor: '#722ed1'
    },
    {
      key: 'resources',
      icon: 'mdi:cpu-64-bit',
      label: '资源使用率',
      value: resourceUsage.value.usage,
      type: 'percentage' as const,
      status: resourceUsage.value.status,
      progress: resourceUsage.value.usage,
      extra: resourceUsage.value.description,
      iconColor: '#fa8c16'
    },
    {
      key: 'errors',
      icon: 'mdi:alert-circle',
      label: '错误率',
      value: errorRate.value.rate,
      type: 'percentage' as const,
      status: errorRate.value.status,
      extra: errorRate.value.statusText,
      iconColor: '#ff4d4f'
    }
  ]);

  return {
    systemStatus,
    taskOverview,
    performanceMetrics,
    resourceUsage,
    errorRate,
    coreMetrics
  };
}

// 注意：辅助函数已移动到 utils/formatters.ts 中
