<script setup lang="ts">
import { ref } from 'vue';
import { NS<PERSON>, NButton, NCard } from 'naive-ui';
import RealtimeMetricsCard from './components/realtime-metrics-card.vue';

// Mock data for demonstration
const mockHealthData = ref<Api.Monitor.AppHealthData>({
  app_info: {
    name: 'StreamForge',
    version: '1.0.0',
    start_time: '2024-01-01T00:00:00Z',
    uptime: 86400,
    status: 'healthy',
    platform: 'Windows',
    python_version: '3.11.0'
  },
  database: {
    status: 'connected',
    size: 2048,
    connection_pool: {
      active: 8,
      idle: 12,
      total: 20
    }
  },
  strm_tasks: {
    status: 'healthy',
    total_tasks: 150,
    running_tasks: 8,
    failed_tasks: 3,
    success_rate: 0.96
  },
  api_performance: {
    status: 'healthy',
    avg_response_time: 0.8,
    request_count_24h: 2500,
    error_rate: 0.02
  }
});

const mockBusinessData = ref<Api.Monitor.BusinessStatsData>({
  strm_tasks: {
    total: 150,
    completed: 135,
    failed: 7,
    running: 5,
    pending: 3,
    success_rate: 0.96, // 使用0-1格式，测试自动转换
    avg_processing_time: 180
  },
  user_activity: {
    total_users: 25,
    active_today: 12,
    login_count_today: 18,
    active_users_week: 20
  },
  api_requests: {
    total_requests_today: 2500,
    avg_response_time: 0.8,
    error_count: 50,
    top_endpoints: []
  }
});

const mockPerformanceData = ref<Api.Monitor.PerformanceData>({
  api_performance: {
    avg_response_time: 0.8,
    request_count: 2500,
    error_rate: 0.02,
    requests_per_minute: 80,
    slowest_endpoints: [],
    status_code_distribution: {}
  },
  database_performance: {
    connection_status: 'connected',
    query_stats: {
      avg_query_time: 0.15,
      slow_queries: 5,
      total_queries: 1200
    }
  },
  system_resources: {
    available: true,
    memory_usage: 72,
    cpu_usage: 58,
    disk_usage: 45
  }
});

const mockSystemData = ref<Api.Monitor.SystemOverviewData>({
  available: true,
  cpu: {
    percent: 58,
    cores_physical: 8,
    cores_logical: 16,
    load_avg: [2.1, 2.3, 2.0]
  },
  memory: {
    total: ***********,
    available: 4831838208,
    used: ***********,
    percent: 72,
    free: 4831838208,
    buffers: 0,
    cached: 0
  }
});

const loading = ref(false);

// Simulate data updates
function simulateDataUpdate() {
  loading.value = true;

  setTimeout(() => {
    // Update some values to simulate real-time changes
    mockBusinessData.value.strm_tasks.running = Math.floor(Math.random() * 10) + 1;
    mockBusinessData.value.strm_tasks.total += Math.floor(Math.random() * 3);
    mockPerformanceData.value.api_performance.avg_response_time = Math.random() * 2 + 0.5;
    mockSystemData.value.cpu!.percent = Math.random() * 40 + 30;
    mockSystemData.value.memory!.percent = Math.random() * 30 + 50;

    loading.value = false;
  }, 1000);
}

// Simulate different system states
function simulateWarningState() {
  mockHealthData.value.app_info.status = 'warning';
  mockPerformanceData.value.api_performance.avg_response_time = 2.5;
  mockPerformanceData.value.api_performance.error_rate = 0.08;
  mockSystemData.value.cpu!.percent = 85;
  mockSystemData.value.memory!.percent = 90;
}

function simulateHealthyState() {
  mockHealthData.value.app_info.status = 'healthy';
  mockPerformanceData.value.api_performance.avg_response_time = 0.6;
  mockPerformanceData.value.api_performance.error_rate = 0.01;
  mockSystemData.value.cpu!.percent = 45;
  mockSystemData.value.memory!.percent = 60;
}

function simulateErrorState() {
  mockHealthData.value.app_info.status = 'error';
  mockPerformanceData.value.api_performance.avg_response_time = 4.2;
  mockPerformanceData.value.api_performance.error_rate = 0.15;
  mockSystemData.value.cpu!.percent = 95;
  mockSystemData.value.memory!.percent = 98;
}
</script>

<template>
  <div class="demo-page">
    <NCard title="重构后的实时监控概览卡片演示" class="mb-6">
      <template #header-extra>
        <NSpace>
          <NButton @click="simulateHealthyState" type="success" size="small">
            正常状态
          </NButton>
          <NButton @click="simulateWarningState" type="warning" size="small">
            警告状态
          </NButton>
          <NButton @click="simulateErrorState" type="error" size="small">
            异常状态
          </NButton>
          <NButton @click="simulateDataUpdate" :loading="loading" size="small">
            模拟数据更新
          </NButton>
        </NSpace>
      </template>

      <div class="demo-description">
        <p>这是重构后的实时监控概览卡片组件演示。重构的主要改进包括：</p>
        <ul>
          <li><strong>简化指标</strong>：从8个指标精简为6个核心指标，提高信息密度</li>
          <li><strong>组件拆分</strong>：创建可复用的MetricItem子组件</li>
          <li><strong>逻辑提取</strong>：使用useMetrics composable管理计算逻辑</li>
          <li><strong>样式简化</strong>：移除复杂渐变，使用简洁的卡片设计</li>
          <li><strong>响应式优化</strong>：改进移动端适配</li>
          <li><strong>类型安全</strong>：完善TypeScript类型定义</li>
        </ul>
      </div>
    </NCard>

    <!-- 重构后的组件 -->
    <RealtimeMetricsCard
      :health-data="mockHealthData"
      :business-data="mockBusinessData"
      :performance-data="mockPerformanceData"
      :system-data="mockSystemData"
      :loading="loading"
    />
  </div>
</template>

<style scoped>
.demo-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-description {
  color: #666;
  line-height: 1.6;
}

.demo-description ul {
  margin: 12px 0;
  padding-left: 20px;
}

.demo-description li {
  margin: 8px 0;
}

.demo-description strong {
  color: #262626;
  font-weight: 600;
}
</style>
