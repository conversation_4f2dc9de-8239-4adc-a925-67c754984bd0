"""
系统监控模块API

提供应用健康监控、业务统计、性能监控等功能的API接口。
基于现有的APILoggerMiddleware、STRM任务系统、用户系统等组件实现监控功能。

注意：异常处理最佳实践
-------------------------------
本项目推荐使用自定义的HTTPException类而不是FastAPI的HTTPException类。

推荐用法:
```python
from app.core.exceptions import HTTPException
raise HTTPException(code="4001", msg="认证失败")
```

而不是:
```python
from fastapi import HTTPException
raise HTTPException(status_code=401, detail="认证失败")
```

系统已添加兼容层处理两种类型的异常，但为保持一致性，请尽量使用自定义HTTPException。
"""

from fastapi import APIRouter, Depends, Request, Query
from fastapi.responses import StreamingResponse
from app.core.dependency import get_current_user, DependPermission, check_token
from app.models.system import User, APILog
from app.models.strm import StrmTask, TaskStatus
from app.schemas.base import Success
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import platform
import asyncio
import json
import logging

logger = logging.getLogger(__name__)
from tortoise import connections
from tortoise.functions import Count, Avg, Max, Min
from tortoise.expressions import Q
from app.core.monitor_cache import MonitorCache, CacheKeys, determine_overall_status
from app.core.performance_analyzer import PerformanceAnalyzer, TaskPerformanceAnalyzer
from app.core.data_aggregator import DataAggregator
from app.models.monitor import MonitorHourlyStats, MonitorDailyStats
from app.core.scheduler import get_scheduler_status

# 可选的系统资源监控
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

router_monitor = APIRouter()

# 应用启动时间（需要在应用启动时设置）
app_start_time = datetime.now()


async def check_database_health() -> Dict[str, Any]:
    """检查数据库健康状态 - 优化版本，减少查询时间"""
    try:
        # 获取数据库连接
        db = connections.get("conn_system")

        # 测试数据库连接 - 使用简单快速的查询
        start_time = datetime.now()
        await db.execute_query("SELECT 1")
        query_time = (datetime.now() - start_time).total_seconds() * 1000  # 转换为毫秒

        # 简化数据库大小获取，避免复杂查询
        db_size = 0  # 暂时不获取大小，减少查询时间
        # 如果需要大小信息，可以在后台任务中定期更新

        return {
            "status": "connected",
            "size": db_size,
            "connection_pool": {
                "active": 1,  # SQLite单连接
                "idle": 0,
                "total": 1
            },
            "query_performance": {
                "avg_response_time": query_time,
                "slow_queries": 0
            }
        }
    except Exception as e:
        return {
            "status": "disconnected",
            "error": str(e),
            "size": 0,
            "connection_pool": {"active": 0, "idle": 0, "total": 0},
            "query_performance": {"avg_response_time": 0.0, "slow_queries": 0}
        }


async def get_strm_task_health() -> Dict[str, Any]:
    """获取STRM任务健康状态"""
    try:
        # 获取最近24小时的任务
        recent_time = datetime.now() - timedelta(hours=24)
        recent_tasks = await StrmTask.filter(create_time__gte=recent_time).all()

        if not recent_tasks:
            return {
                "status": "healthy",
                "total_tasks": 0,
                "running_tasks": 0,
                "failed_tasks": 0,
                "success_rate": 1.0,
                "recent_failures": 0
            }

        running_count = len([t for t in recent_tasks if t.status == TaskStatus.RUNNING])
        failed_count = len([t for t in recent_tasks if t.status == TaskStatus.FAILED])
        completed_count = len([t for t in recent_tasks if t.status == TaskStatus.COMPLETED])

        success_rate = completed_count / len(recent_tasks) if recent_tasks else 1.0

        # 判断健康状态
        if failed_count > len(recent_tasks) * 0.3:  # 失败率超过30%
            status = "error"
        elif failed_count > len(recent_tasks) * 0.1:  # 失败率超过10%
            status = "warning"
        else:
            status = "healthy"

        return {
            "status": status,
            "total_tasks": len(recent_tasks),
            "running_tasks": running_count,
            "failed_tasks": failed_count,
            "success_rate": success_rate,
            "recent_failures": failed_count
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "total_tasks": 0,
            "running_tasks": 0,
            "failed_tasks": 0,
            "success_rate": 0.0,
            "recent_failures": 0
        }


async def get_api_health_summary() -> Dict[str, Any]:
    """获取API健康状态摘要"""
    try:
        # 获取最近1小时的API日志
        recent_time = datetime.now() - timedelta(hours=1)

        # 排除一些非业务请求的路径，避免影响健康状态判断
        excluded_paths = [
            '/favicon.ico',
            '/docs',
            '/openapi.json',
            '/static/',
            '/.well-known/',
            '/api/v1/monitor/realtime-stream'  # 实时监控流不计入错误统计
        ]

        api_logs = await APILog.filter(
            create_time__gte=recent_time,
            process_time__isnull=False
        ).all()

        # 过滤掉非业务请求
        business_logs = []
        for log in api_logs:
            is_excluded = False
            for excluded_path in excluded_paths:
                if log.request_path and excluded_path in log.request_path:
                    is_excluded = True
                    break
            if not is_excluded:
                business_logs.append(log)

        if not business_logs:
            return {
                "status": "healthy",
                "avg_response_time": 0.0,
                "request_count_24h": 0,
                "error_rate": 0.0,
                "slowest_endpoints": []
            }

        # 计算基础统计（只统计业务请求）
        process_times = [log.process_time for log in business_logs if log.process_time]
        response_codes = [log.response_code for log in business_logs if log.response_code]

        avg_response_time = sum(process_times) / len(process_times) if process_times else 0
        error_count = len([c for c in response_codes if c and int(c) >= 400])
        error_rate = error_count / len(response_codes) if response_codes else 0

        # 调整健康状态判断阈值，更适合实际业务场景
        if error_rate > 0.2 or avg_response_time > 10:  # 错误率>20%或平均响应时间>10秒
            status = "error"
        elif error_rate > 0.1 or avg_response_time > 5:  # 错误率>10%或平均响应时间>5秒
            status = "warning"
        else:
            status = "healthy"

        return {
            "status": status,
            "avg_response_time": avg_response_time,
            "request_count_24h": len(api_logs),  # 总请求数（包含所有请求）
            "error_rate": error_rate,  # 业务请求错误率
            "slowest_endpoints": []  # TODO: 实现最慢端点统计
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "avg_response_time": 0.0,
            "request_count_24h": 0,
            "error_rate": 0.0,
            "slowest_endpoints": []
        }


def determine_overall_status(db_status: Dict, strm_health: Dict, api_health: Dict) -> str:
    """确定整体应用状态"""
    if db_status.get("status") == "disconnected":
        return "error"

    if strm_health.get("status") == "error" or api_health.get("status") == "error":
        return "error"

    if strm_health.get("status") == "warning" or api_health.get("status") == "warning":
        return "warning"

    return "healthy"


@router_monitor.get("/app-health", summary="获取应用健康状态")
async def get_app_health(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
):
    """
    获取应用健康状态

    基于现有组件获取：
    - 应用基本信息（启动时间、运行时长等）
    - 数据库连接状态
    - STRM任务健康状态
    - API性能健康状态
    """
    async def fetch_app_health():
        uptime_seconds = int((datetime.now() - app_start_time).total_seconds())

        # 检查各个组件的健康状态
        db_status = await check_database_health()
        strm_health = await get_strm_task_health()
        api_health = await get_api_health_summary()

        # 确定整体状态
        overall_status = determine_overall_status(db_status, strm_health, api_health)

        return {
            "app_info": {
                "name": "fast-soy-admin",
                "version": "1.0.0",
                "start_time": app_start_time.isoformat(),
                "uptime": uptime_seconds,
                "status": overall_status,
                "platform": platform.system(),
                "python_version": platform.python_version()
            },
            "database": db_status,
            "strm_tasks": strm_health,
            "api_performance": api_health
        }

    # 使用缓存，30秒TTL
    data = await MonitorCache.get_or_set(
        CacheKeys.APP_HEALTH,
        fetch_app_health,
        ttl_seconds=30
    )

    return Success(data=data)


@router_monitor.get("/business-stats", summary="获取业务统计数据")
async def get_business_stats(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
):
    """
    获取业务统计数据

    基于现有模型获取：
    - STRM任务统计（基于StrmTask模型）
    - 用户活动统计（基于User模型）
    - API请求统计（基于APILog模型）
    """
    try:
        # 获取STRM任务统计
        strm_stats = await StrmTask.annotate(
            total=Count('id'),
            completed=Count('id', _filter=Q(status=TaskStatus.COMPLETED)),
            failed=Count('id', _filter=Q(status=TaskStatus.FAILED)),
            running=Count('id', _filter=Q(status=TaskStatus.RUNNING)),
            pending=Count('id', _filter=Q(status=TaskStatus.PENDING))
        ).values('total', 'completed', 'failed', 'running', 'pending')

        strm_data = strm_stats[0] if strm_stats else {
            'total': 0, 'completed': 0, 'failed': 0, 'running': 0, 'pending': 0
        }

        # 计算成功率
        success_rate = (strm_data['completed'] / strm_data['total']) if strm_data['total'] > 0 else 0.0

        # 计算平均处理时间（基于已完成的任务）
        completed_tasks = await StrmTask.filter(
            status=TaskStatus.COMPLETED,
            start_time__isnull=False,
            end_time__isnull=False
        ).all()

        avg_processing_time = 0.0
        if completed_tasks:
            processing_times = []
            for task in completed_tasks:
                if task.start_time and task.end_time:
                    duration = (task.end_time - task.start_time).total_seconds()
                    processing_times.append(duration)
            avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0.0

        # 获取用户活动统计
        today = datetime.now().date()
        week_ago = datetime.now() - timedelta(days=7)

        total_users = await User.all().count()
        active_today = await User.filter(last_login__date=today).count()
        active_users_week = await User.filter(last_login__gte=week_ago).count()

        # 获取API请求统计
        today_start = datetime.combine(today, datetime.min.time())
        today_logs = await APILog.filter(create_time__gte=today_start).all()

        # 统计响应时间
        process_times = [log.process_time for log in today_logs if log.process_time]
        avg_response_time = sum(process_times) / len(process_times) if process_times else 0.0

        # 统计错误
        error_count = len([log for log in today_logs if log.response_code and int(log.response_code) >= 400])

        # 统计热门端点
        endpoint_counts = {}
        for log in today_logs:
            path = log.request_path
            endpoint_counts[path] = endpoint_counts.get(path, 0) + 1

        top_endpoints = sorted(endpoint_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        top_endpoints = [{"endpoint": ep, "count": count} for ep, count in top_endpoints]

        return Success(data={
            "strm_tasks": {
                "total": strm_data['total'],
                "completed": strm_data['completed'],
                "failed": strm_data['failed'],
                "running": strm_data['running'],
                "pending": strm_data['pending'],
                "success_rate": success_rate,
                "avg_processing_time": avg_processing_time
            },
            "user_activity": {
                "total_users": total_users,
                "active_today": active_today,
                "login_count_today": active_today,  # 简化处理，假设活跃用户就是今日登录用户
                "active_users_week": active_users_week
            },
            "api_requests": {
                "total_requests_today": len(today_logs),
                "avg_response_time": avg_response_time,
                "error_count": error_count,
                "top_endpoints": top_endpoints
            }
        })
    except Exception as e:
        # 如果出现错误，返回默认值
        return Success(data={
            "strm_tasks": {
                "total": 0,
                "completed": 0,
                "failed": 0,
                "running": 0,
                "pending": 0,
                "success_rate": 0.0,
                "avg_processing_time": 0.0
            },
            "user_activity": {
                "total_users": 0,
                "active_today": 0,
                "login_count_today": 0,
                "active_users_week": 0
            },
            "api_requests": {
                "total_requests_today": 0,
                "avg_response_time": 0.0,
                "error_count": 0,
                "top_endpoints": []
            },
            "error": str(e)
        })


@router_monitor.get("/performance", summary="获取性能监控数据")
async def get_performance_data(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
    minutes: int = 60,  # 默认查询最近60分钟的数据
):
    """
    获取性能监控数据

    基于APILoggerMiddleware收集的数据分析：
    - API性能统计
    - 数据库性能
    - 系统资源使用（可选）
    """
    try:
        # 获取指定时间范围内的API日志 - 优化查询逻辑
        cutoff_time = datetime.now() - timedelta(minutes=minutes)

        # 使用聚合查询获取基础统计，避免加载所有记录
        from tortoise.functions import Count, Avg, Max
        from tortoise import connections

        # 获取基础统计数据
        basic_stats = await APILog.filter(
            create_time__gte=cutoff_time,
            process_time__isnull=False
        ).annotate(
            total_count=Count('id'),
            avg_time=Avg('process_time'),
            max_time=Max('process_time')
        ).values('total_count', 'avg_time', 'max_time')

        if not basic_stats or basic_stats[0]['total_count'] == 0:
            api_performance = {
                "avg_response_time": 0.0,
                "request_count": 0,
                "error_rate": 0.0,
                "requests_per_minute": 0.0,
                "slowest_endpoints": [],
                "status_code_distribution": {}
            }
        else:
            stats = basic_stats[0]
            total_requests = stats['total_count']
            avg_response_time = float(stats['avg_time']) if stats['avg_time'] else 0.0

            # 获取错误率 - 使用聚合查询
            error_count = await APILog.filter(
                create_time__gte=cutoff_time,
                response_code__gte='400'
            ).count()

            error_rate = error_count / total_requests if total_requests > 0 else 0.0

            # 获取状态码分布 - 使用原生SQL优化
            conn = connections.get("conn_system")
            status_sql = """
                SELECT response_code, COUNT(*) as count
                FROM api_logs
                WHERE create_time >= ? AND response_code IS NOT NULL
                GROUP BY response_code
                ORDER BY count DESC
            """
            status_result = await conn.execute_query(status_sql, [cutoff_time])
            status_distribution = {
                str(row['response_code']): row['count']
                for row in (status_result[1] or [])
            }

            # 获取最慢的端点 - 使用原生SQL优化
            endpoint_sql = """
                SELECT
                    request_method || ' ' || request_path as endpoint,
                    AVG(process_time) as avg_time,
                    COUNT(*) as count,
                    MAX(process_time) as max_time
                FROM api_logs
                WHERE create_time >= ? AND process_time IS NOT NULL
                GROUP BY request_method, request_path
                ORDER BY avg_time DESC
                LIMIT 5
            """
            endpoint_result = await conn.execute_query(endpoint_sql, [cutoff_time])
            slowest_endpoints = [
                {
                    "endpoint": row['endpoint'],
                    "avg_time": float(row['avg_time']),
                    "count": row['count'],
                    "max_time": float(row['max_time'])
                }
                for row in (endpoint_result[1] or [])
            ]

            api_performance = {
                "avg_response_time": avg_response_time,
                "request_count": total_requests,
                "error_rate": error_rate,
                "requests_per_minute": total_requests / minutes,
                "slowest_endpoints": slowest_endpoints,
                "status_code_distribution": status_distribution
            }

        # 使用异步并发执行数据库健康检查和系统资源检查
        import asyncio

        async def get_db_performance():
            db_health = await check_database_health()
            return {
                "connection_status": db_health.get("status", "unknown"),
                "query_stats": {
                    "avg_query_time": db_health.get("query_performance", {}).get("avg_response_time", 0.0),
                    "slow_queries": 0,
                    "total_queries": api_performance.get("request_count", 0)
                }
            }

        async def get_system_resources():
            system_resources = {
                "available": PSUTIL_AVAILABLE
            }

            if PSUTIL_AVAILABLE:
                try:
                    # 获取系统资源信息 - 优化CPU检查，减少阻塞时间
                    memory = psutil.virtual_memory()
                    # 使用更短的interval减少阻塞时间
                    cpu_percent = psutil.cpu_percent(interval=0.1)
                    disk = psutil.disk_usage('/')

                    system_resources.update({
                        "memory_usage": memory.percent,
                        "cpu_usage": cpu_percent,
                        "disk_usage": disk.percent,
                        "memory_total": memory.total,
                        "memory_available": memory.available,
                        "disk_total": disk.total,
                        "disk_free": disk.free
                    })
                except Exception as e:
                    system_resources.update({
                        "memory_usage": None,
                        "cpu_usage": None,
                        "disk_usage": None,
                        "error": str(e)
                    })
            else:
                system_resources.update({
                    "memory_usage": None,
                    "cpu_usage": None,
                    "disk_usage": None
                })

            return system_resources

        # 并发执行数据库和系统资源检查
        database_performance, system_resources = await asyncio.gather(
            get_db_performance(),
            get_system_resources()
        )

        return Success(data={
            "api_performance": api_performance,
            "database_performance": database_performance,
            "system_resources": system_resources
        })

    except Exception as e:
        return Success(data={
            "api_performance": {
                "avg_response_time": 0.0,
                "request_count": 0,
                "error_rate": 0.0,
                "requests_per_minute": 0.0,
                "slowest_endpoints": []
            },
            "database_performance": {
                "connection_status": "unknown",
                "query_stats": {
                    "avg_query_time": 0.0,
                    "slow_queries": 0,
                    "total_queries": 0
                }
            },
            "system_resources": {
                "available": PSUTIL_AVAILABLE,
                "memory_usage": None,
                "cpu_usage": None,
                "disk_usage": None
            },
            "error": str(e)
        })


@router_monitor.get("/system-overview", summary="获取系统资源概览")
async def get_system_overview(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
):
    """
    获取系统资源概览

    提供系统资源使用情况监控：
    - CPU使用率和核心数
    - 内存使用情况
    - 磁盘空间使用
    - 网络I/O统计
    """
    async def fetch_system_overview():
        if not PSUTIL_AVAILABLE:
            return {
                "error": "系统资源监控功能未启用，psutil依赖不可用",
                "available": False
            }

        try:
            import psutil
            import os

            # CPU信息
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            cpu_count_logical = psutil.cpu_count(logical=True)

            # 获取CPU负载平均值（仅在Unix系统上可用）
            load_avg = []
            try:
                if hasattr(psutil, 'getloadavg'):
                    load_avg = list(psutil.getloadavg())
            except (AttributeError, OSError):
                # Windows系统不支持getloadavg
                load_avg = []

            # 内存信息
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()

            # 磁盘信息（获取根目录的磁盘使用情况）
            disk_usage = psutil.disk_usage('/')

            # 网络I/O统计
            net_io = psutil.net_io_counters()

            # 网络连接数
            try:
                connections = len(psutil.net_connections())
            except (psutil.AccessDenied, OSError):
                # 某些系统可能需要管理员权限
                connections = 0

            # 程序自身的进程监控
            current_process = psutil.Process(os.getpid())

            # 获取程序自身的CPU使用率（需要一定时间间隔）
            try:
                app_cpu_percent = current_process.cpu_percent(interval=0.1)
            except:
                app_cpu_percent = 0.0

            # 获取程序自身的内存使用情况
            app_memory_info = current_process.memory_info()
            app_memory_percent = current_process.memory_percent()

            # 获取程序的线程数
            try:
                app_threads = current_process.num_threads()
            except:
                app_threads = 0

            # 获取程序的文件描述符数量（Unix系统）
            try:
                app_fds = current_process.num_fds()
            except (AttributeError, psutil.AccessDenied):
                # Windows系统不支持num_fds
                app_fds = 0

            # 获取程序的运行时间
            app_create_time = current_process.create_time()
            app_uptime = datetime.now().timestamp() - app_create_time

            # 进程信息
            process_count = len(psutil.pids())

            # 系统启动时间
            boot_time = psutil.boot_time()
            uptime_seconds = int((datetime.now().timestamp() - boot_time))

            return {
                "available": True,
                "timestamp": datetime.now().isoformat(),
                "cpu": {
                    "percent": round(cpu_percent, 2),
                    "cores_physical": cpu_count,
                    "cores_logical": cpu_count_logical,
                    "load_avg": [round(x, 2) for x in load_avg] if load_avg else [],
                    "frequency": {
                        "current": round(psutil.cpu_freq().current, 2) if psutil.cpu_freq() else 0,
                        "min": round(psutil.cpu_freq().min, 2) if psutil.cpu_freq() else 0,
                        "max": round(psutil.cpu_freq().max, 2) if psutil.cpu_freq() else 0
                    } if psutil.cpu_freq() else None
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "percent": round(memory.percent, 2),
                    "free": memory.free,
                    "buffers": getattr(memory, 'buffers', 0),
                    "cached": getattr(memory, 'cached', 0)
                },
                "application": {
                    "name": "StreamForge",
                    "pid": os.getpid(),
                    "cpu_percent": round(app_cpu_percent, 2),
                    "memory": {
                        "rss": app_memory_info.rss,  # 物理内存使用量
                        "vms": app_memory_info.vms,  # 虚拟内存使用量
                        "percent": round(app_memory_percent, 2),  # 内存使用百分比
                        "rss_mb": round(app_memory_info.rss / 1024 / 1024, 2),  # MB
                        "vms_mb": round(app_memory_info.vms / 1024 / 1024, 2)   # MB
                    },
                    "threads": app_threads,
                    "file_descriptors": app_fds,
                    "uptime_seconds": round(app_uptime, 2),
                    "uptime_formatted": str(timedelta(seconds=int(app_uptime))),
                    "create_time": datetime.fromtimestamp(app_create_time).isoformat()
                },
                "swap": {
                    "total": swap.total,
                    "used": swap.used,
                    "free": swap.free,
                    "percent": round(swap.percent, 2)
                },
                "disk": {
                    "total": disk_usage.total,
                    "used": disk_usage.used,
                    "free": disk_usage.free,
                    "percent": round((disk_usage.used / disk_usage.total) * 100, 2)
                },
                "network": {
                    "bytes_sent": net_io.bytes_sent,
                    "bytes_recv": net_io.bytes_recv,
                    "packets_sent": net_io.packets_sent,
                    "packets_recv": net_io.packets_recv,
                    "errin": net_io.errin,
                    "errout": net_io.errout,
                    "dropin": net_io.dropin,
                    "dropout": net_io.dropout,
                    "connections": connections
                },
                "system": {
                    "process_count": process_count,
                    "boot_time": datetime.fromtimestamp(boot_time).isoformat(),
                    "uptime_seconds": uptime_seconds,
                    "platform": platform.system(),
                    "platform_release": platform.release(),
                    "platform_version": platform.version(),
                    "architecture": platform.machine()
                }
            }

        except Exception as e:
            return {
                "error": f"获取系统资源信息失败: {str(e)}",
                "available": False
            }

    return Success(data=await MonitorCache.get_or_set(
        CacheKeys.SYSTEM_OVERVIEW,
        fetch_system_overview,
        ttl_seconds=30  # 30秒缓存，系统资源信息更新较频繁
    ))


@router_monitor.get("/error-analysis", summary="获取错误分析数据")
async def get_error_analysis(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
    hours: int = 24,  # 默认分析最近24小时
    error_type: Optional[str] = None,
):
    """
    获取错误分析数据

    基于现有日志系统和STRM任务失败分析：
    - API错误统计
    - STRM任务失败分析
    - 错误趋势分析
    """
    async def fetch_error_analysis():
        cutoff_time = datetime.now() - timedelta(hours=hours)

        # 获取API错误统计
        api_error_logs = await APILog.filter(
            create_time__gte=cutoff_time,
            response_code__gte=400
        ).all()

        # 获取所有API请求用于计算错误率
        total_api_requests = await APILog.filter(
            create_time__gte=cutoff_time
        ).count()

        # 统计错误类型分布
        error_by_code = {}
        error_by_endpoint = {}

        for log in api_error_logs:
            # 按状态码统计
            code = str(log.response_code) if log.response_code else 'unknown'
            error_by_code[code] = error_by_code.get(code, 0) + 1

            # 按端点统计
            endpoint = log.request_path or 'unknown'
            if endpoint not in error_by_endpoint:
                error_by_endpoint[endpoint] = {
                    'count': 0,
                    'methods': set(),
                    'codes': set()
                }
            error_by_endpoint[endpoint]['count'] += 1
            error_by_endpoint[endpoint]['methods'].add(log.request_method or 'unknown')
            error_by_endpoint[endpoint]['codes'].add(code)

        # 转换为列表格式
        top_errors = []
        for code, count in sorted(error_by_code.items(), key=lambda x: x[1], reverse=True)[:10]:
            error_name = {
                '400': 'Bad Request',
                '401': 'Unauthorized',
                '403': 'Forbidden',
                '404': 'Not Found',
                '500': 'Internal Server Error',
                '502': 'Bad Gateway',
                '503': 'Service Unavailable'
            }.get(code, f'HTTP {code}')

            top_errors.append({
                'error_type': error_name,
                'error_code': code,
                'count': count,
                'percentage': (count / len(api_error_logs) * 100) if api_error_logs else 0
            })

        # 获取STRM任务失败统计
        failed_tasks = await StrmTask.filter(
            create_time__gte=cutoff_time,
            status=TaskStatus.FAILED
        ).all()

        total_tasks = await StrmTask.filter(
            create_time__gte=cutoff_time
        ).count()

        # 按任务类型统计失败
        task_failures_by_type = {}
        for task in failed_tasks:
            task_type = 'STRM生成'  # 目前只有一种任务类型
            task_failures_by_type[task_type] = task_failures_by_type.get(task_type, 0) + 1

        # 生成趋势数据（按小时统计）
        trend_data = []
        for i in range(hours):
            hour_start = cutoff_time + timedelta(hours=i)
            hour_end = hour_start + timedelta(hours=1)

            # 该小时的API错误
            hour_api_errors = await APILog.filter(
                create_time__gte=hour_start,
                create_time__lt=hour_end,
                response_code__gte=400
            ).count()

            # 该小时的总请求
            hour_total_requests = await APILog.filter(
                create_time__gte=hour_start,
                create_time__lt=hour_end
            ).count()

            # 该小时的任务失败
            hour_task_failures = await StrmTask.filter(
                create_time__gte=hour_start,
                create_time__lt=hour_end,
                status=TaskStatus.FAILED
            ).count()

            trend_data.append({
                'timestamp': hour_start.isoformat(),
                'api_error_count': hour_api_errors,
                'api_error_rate': (hour_api_errors / hour_total_requests) if hour_total_requests > 0 else 0,
                'task_failure_count': hour_task_failures,
                'total_requests': hour_total_requests
            })

        return {
            "error_summary": {
                "total_errors": len(api_error_logs),
                "total_requests": total_api_requests,
                "error_rate": (len(api_error_logs) / total_api_requests) if total_api_requests > 0 else 0,
                "top_errors": top_errors,
                "top_error_endpoints": [
                    {
                        "endpoint": endpoint,
                        "count": data['count'],
                        "methods": list(data['methods']),
                        "error_codes": list(data['codes'])
                    }
                    for endpoint, data in sorted(error_by_endpoint.items(), key=lambda x: x[1]['count'], reverse=True)[:10]
                ]
            },
            "failed_tasks": {
                "total": len(failed_tasks),
                "total_tasks": total_tasks,
                "failure_rate": (len(failed_tasks) / total_tasks) if total_tasks > 0 else 0,
                "by_type": [
                    {"type": task_type, "count": count}
                    for task_type, count in task_failures_by_type.items()
                ]
            },
            "trend_data": trend_data,
            "analysis_period_hours": hours
        }

    return Success(data=await MonitorCache.get_or_set(
        f"{CacheKeys.ERROR_ANALYSIS}:{hours}h",
        fetch_error_analysis,
        ttl_seconds=300  # 5分钟缓存
    ))


# 内部数据获取函数，用于实时流
async def _get_app_health_data():
    """内部函数：获取应用健康状态数据"""
    async def fetch_app_health():
        uptime_seconds = int((datetime.now() - app_start_time).total_seconds())

        # 检查各个组件的健康状态
        db_status = await check_database_health()
        strm_health = await get_strm_task_health()
        api_health = await get_api_health_summary()

        # 确定整体状态
        overall_status = determine_overall_status(db_status, strm_health, api_health)

        return {
            "app_info": {
                "name": "fast-soy-admin",
                "version": "1.0.0",
                "start_time": app_start_time.isoformat(),
                "uptime": uptime_seconds,
                "status": overall_status,
                "platform": platform.system(),
                "python_version": platform.python_version()
            },
            "database": db_status,
            "strm_tasks": strm_health,
            "api_performance": api_health
        }

    # 使用缓存，30秒TTL
    return await MonitorCache.get_or_set(
        CacheKeys.APP_HEALTH,
        fetch_app_health,
        ttl_seconds=30
    )


async def _get_business_stats_data():
    """内部函数：获取业务统计数据"""
    try:
        # 获取STRM任务统计
        strm_stats = await StrmTask.annotate(
            total=Count('id'),
            completed=Count('id', _filter=Q(status=TaskStatus.COMPLETED)),
            failed=Count('id', _filter=Q(status=TaskStatus.FAILED)),
            running=Count('id', _filter=Q(status=TaskStatus.RUNNING)),
            pending=Count('id', _filter=Q(status=TaskStatus.PENDING))
        ).values('total', 'completed', 'failed', 'running', 'pending')

        strm_data = strm_stats[0] if strm_stats else {
            'total': 0, 'completed': 0, 'failed': 0, 'running': 0, 'pending': 0
        }

        # 计算成功率和平均处理时间
        success_rate = (strm_data['completed'] / strm_data['total'] * 100) if strm_data['total'] > 0 else 0.0

        # 获取已完成任务的平均处理时间
        completed_tasks = await StrmTask.filter(
            status=TaskStatus.COMPLETED,
            end_time__isnull=False,
            start_time__isnull=False
        ).all()

        if completed_tasks:
            total_time = sum(
                (task.end_time - task.start_time).total_seconds()
                for task in completed_tasks
                if task.end_time and task.start_time
            )
            avg_processing_time = total_time / len(completed_tasks)
        else:
            avg_processing_time = 0.0

        strm_data.update({
            'success_rate': round(success_rate, 2),
            'avg_processing_time': round(avg_processing_time, 2)
        })

        # 获取用户活动统计
        total_users = await User.all().count()
        today = datetime.now().date()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())
        active_today = await User.filter(last_login__gte=today_start, last_login__lte=today_end).count()

        # 获取今日登录次数（基于APILog）
        login_count_today = await APILog.filter(
            request_path__icontains='login',
            create_time__gte=today_start,
            create_time__lte=today_end
        ).count()

        # 获取本周活跃用户
        week_ago = datetime.now() - timedelta(days=7)
        active_users_week = await User.filter(last_login__gte=week_ago).count()

        user_data = {
            "total_users": total_users,
            "active_today": active_today,
            "login_count_today": login_count_today,
            "active_users_week": active_users_week
        }

        # 获取API请求统计
        total_requests_today = await APILog.filter(
            create_time__gte=today_start,
            create_time__lte=today_end
        ).count()

        # 计算平均响应时间
        api_logs_today = await APILog.filter(
            create_time__gte=today_start,
            create_time__lte=today_end,
            process_time__isnull=False
        ).all()

        if api_logs_today:
            avg_response_time = sum(log.process_time for log in api_logs_today) / len(api_logs_today)
        else:
            avg_response_time = 0.0

        # 获取错误数量
        error_count = await APILog.filter(
            create_time__gte=today_start,
            create_time__lte=today_end,
            response_code__gte='400'
        ).count()

        # 获取热门端点
        top_endpoints_data = await APILog.filter(
            create_time__gte=today_start,
            create_time__lte=today_end
        ).annotate(
            count=Count('request_path')
        ).group_by('request_path').order_by('-count').limit(5).values('request_path', 'count')

        api_data = {
            "total_requests_today": total_requests_today,
            "avg_response_time": round(avg_response_time, 2),
            "error_count": error_count,
            "top_endpoints": [{"endpoint": item["request_path"], "count": item["count"]} for item in top_endpoints_data]
        }

        return {
            "strm_tasks": strm_data,
            "user_activity": user_data,
            "api_requests": api_data
        }

    except Exception as e:
        # 如果出现错误，返回默认值
        return {
            "strm_tasks": {
                "total": 0,
                "completed": 0,
                "failed": 0,
                "running": 0,
                "pending": 0,
                "success_rate": 0.0,
                "avg_processing_time": 0.0
            },
            "user_activity": {
                "total_users": 0,
                "active_today": 0,
                "login_count_today": 0,
                "active_users_week": 0
            },
            "api_requests": {
                "total_requests_today": 0,
                "avg_response_time": 0.0,
                "error_count": 0,
                "top_endpoints": []
            },
            "error": str(e)
        }


async def _get_performance_data(minutes: int = 60):
    """内部函数：获取性能监控数据 - 优化版本"""
    async def fetch_performance_data():
        # 使用优化的性能分析器
        performance_data = await PerformanceAnalyzer.get_fast_performance_stats(minutes)
        return performance_data

        if api_logs:
            response_times = [log.process_time for log in api_logs]
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)

            # 计算P95响应时间
            sorted_times = sorted(response_times)
            p95_index = int(len(sorted_times) * 0.95)
            p95_response_time = sorted_times[p95_index] if sorted_times else 0
        else:
            avg_response_time = max_response_time = min_response_time = p95_response_time = 0

        # 获取错误率
        total_requests = len(api_logs)
        error_requests = len([log for log in api_logs if log.response_code and int(log.response_code) >= 400])
        error_rate = (error_requests / total_requests * 100) if total_requests > 0 else 0

        # 获取请求量趋势（按小时分组）
        hourly_stats = {}
        for log in api_logs:
            hour_key = log.create_time.strftime('%H:00')
            if hour_key not in hourly_stats:
                hourly_stats[hour_key] = {'requests': 0, 'errors': 0, 'total_time': 0}

            hourly_stats[hour_key]['requests'] += 1
            if log.response_code and int(log.response_code) >= 400:
                hourly_stats[hour_key]['errors'] += 1
            if log.process_time:
                hourly_stats[hour_key]['total_time'] += log.process_time

        # 转换为图表数据格式
        trend_data = []
        for hour, stats in sorted(hourly_stats.items()):
            avg_time = stats['total_time'] / stats['requests'] if stats['requests'] > 0 else 0
            trend_data.append({
                'time': hour,
                'requests': stats['requests'],
                'avg_response_time': round(avg_time, 2),
                'error_rate': round(stats['errors'] / stats['requests'] * 100, 2) if stats['requests'] > 0 else 0
            })

        return {
            "summary": {
                "total_requests": total_requests,
                "avg_response_time": round(avg_response_time, 2),
                "max_response_time": round(max_response_time, 2),
                "min_response_time": round(min_response_time, 2),
                "p95_response_time": round(p95_response_time, 2),
                "error_rate": round(error_rate, 2),
                "time_range": f"{minutes}分钟"
            },
            "trends": trend_data
        }

    # 使用缓存，根据时间范围设置不同的TTL - 优化缓存时间
    cache_key = CacheKeys.get_performance_key(minutes)
    # 增加缓存时间：1小时内数据缓存3分钟，否则10分钟
    ttl = 180 if minutes <= 60 else 600

    return await MonitorCache.get_or_set(
        cache_key,
        fetch_performance_data,
        ttl_seconds=ttl
    )


@router_monitor.get("/realtime-stream", summary="SSE实时监控数据流")
async def realtime_monitor_stream(
    request: Request,
    token: Optional[str] = Query(None, description="访问令牌"),
):
    """
    SSE实时监控数据流

    提供Server-Sent Events实时数据推送，包括：
    - 应用健康状态
    - 业务统计数据
    - 性能监控数据

    支持通过查询参数传递token进行认证：
    /api/v1/monitor/realtime-stream?token=your_access_token
    """

    # 认证用户
    current_user = None

    # 方法1: 从请求头获取token
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        bearer_token = auth_header.replace("Bearer ", "")
        if bearer_token:
            status, code, decode_data = check_token(bearer_token)
            if status and decode_data["data"]["tokenType"] == "accessToken":
                user_id = decode_data["data"]["userId"]
                current_user = await User.filter(id=user_id).first()

    # 方法2: 从查询参数获取token
    if current_user is None and token:
        status, code, decode_data = check_token(token)
        if status and decode_data["data"]["tokenType"] == "accessToken":
            user_id = decode_data["data"]["userId"]
            current_user = await User.filter(id=user_id).first()

    # 如果认证失败，返回错误流
    if not current_user:
        async def error_stream():
            error_data = {
                "timestamp": datetime.now().isoformat(),
                "error": "Authentication failed, token does not exists in the request.",
                "type": "auth_error"
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"

        return StreamingResponse(
            error_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )

    async def generate_monitor_data():
        """生成实时监控数据"""
        logger.info(f"SSE监控数据流已启动，用户: {current_user.user_name}")

        while True:
            try:
                current_time = datetime.now()
                logger.debug(f"开始获取监控数据 - {current_time.isoformat()}")

                # 获取实时监控数据 - 直接获取数据而不是调用API端点
                try:
                    # 直接获取应用健康状态数据
                    health_data = await _get_app_health_data()
                    logger.debug("成功获取健康状态数据")

                    # 直接获取业务统计数据
                    business_data = await _get_business_stats_data()
                    logger.debug("成功获取业务统计数据")

                    # 直接获取性能监控数据 - 转换为与/performance端点一致的格式
                    raw_performance_data = await _get_performance_data(60)

                    # 转换数据格式以匹配Api.Monitor.PerformanceData类型
                    if raw_performance_data and "summary" in raw_performance_data:
                        summary = raw_performance_data["summary"]
                        performance_data = {
                            "api_performance": {
                                "avg_response_time": summary.get("avg_response_time", 0.0),
                                "request_count": summary.get("total_requests", 0),
                                "error_rate": summary.get("error_rate", 0.0) / 100,  # 转换为小数
                                "requests_per_minute": summary.get("total_requests", 0) / 60,  # 估算每分钟请求数
                                "slowest_endpoints": [],
                                "status_code_distribution": {}
                            },
                            "database_performance": {
                                "connection_status": "unknown",
                                "query_stats": {
                                    "avg_query_time": 0.0,
                                    "slow_queries": 0,
                                    "total_queries": summary.get("total_requests", 0)
                                }
                            },
                            "system_resources": {
                                "available": False
                            }
                        }
                    else:
                        # 如果没有数据，返回默认结构
                        performance_data = {
                            "api_performance": {
                                "avg_response_time": 0.0,
                                "request_count": 0,
                                "error_rate": 0.0,
                                "requests_per_minute": 0.0,
                                "slowest_endpoints": [],
                                "status_code_distribution": {}
                            },
                            "database_performance": {
                                "connection_status": "unknown",
                                "query_stats": {
                                    "avg_query_time": 0.0,
                                    "slow_queries": 0,
                                    "total_queries": 0
                                }
                            },
                            "system_resources": {
                                "available": False
                            }
                        }

                    logger.debug("成功获取性能监控数据")

                except Exception as e:
                    logger.error(f"获取监控数据失败: {str(e)}", exc_info=True)
                    # 如果获取数据失败，发送错误信息
                    error_data = {
                        "timestamp": current_time.isoformat(),
                        "error": f"获取监控数据失败: {str(e)}",
                        "type": "data_error"
                    }
                    yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
                    await asyncio.sleep(10)  # 出错后等待10秒重试
                    continue

                # 构造SSE消息
                data = {
                    "timestamp": current_time.isoformat(),
                    "health": health_data,
                    "business": business_data,
                    "performance": performance_data
                }

                logger.debug(f"发送监控数据 - {current_time.isoformat()}")
                yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"

                # 等待30秒，期间每10秒发送一次心跳
                for i in range(3):
                    await asyncio.sleep(10)
                    # 发送心跳消息
                    heartbeat = {
                        "timestamp": datetime.now().isoformat(),
                        "type": "heartbeat",
                        "message": "连接正常"
                    }
                    logger.debug(f"发送心跳 - {heartbeat['timestamp']}")
                    yield f"data: {json.dumps(heartbeat, ensure_ascii=False)}\n\n"

            except asyncio.CancelledError:
                logger.info(f"SSE监控数据流被取消，用户: {current_user.user_name}")
                break
            except Exception as e:
                logger.error(f"SSE监控数据流异常: {str(e)}", exc_info=True)
                try:
                    error_data = {
                        "timestamp": datetime.now().isoformat(),
                        "error": f"监控流异常: {str(e)}",
                        "type": "stream_error"
                    }
                    yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
                except Exception:
                    # 如果连发送错误消息都失败了，直接退出
                    logger.error("无法发送错误消息，SSE流将终止")
                    break
                await asyncio.sleep(5)  # 出错后5秒重试

        logger.info(f"SSE监控数据流已结束，用户: {current_user.user_name}")

    return StreamingResponse(
        generate_monitor_data(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )


@router_monitor.get("/strm-task-stats", summary="获取STRM任务详细统计")
async def get_strm_task_stats(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
    hours: int = 24,  # 默认统计最近24小时
):
    """
    获取STRM任务详细统计数据

    提供详细的STRM任务分析：
    - 任务状态分布
    - 处理时间统计
    - 处理速度趋势
    - 任务队列状态
    """
    async def fetch_strm_task_stats():
        cutoff_time = datetime.now() - timedelta(hours=hours)

        # 获取基础任务统计
        all_tasks = await StrmTask.filter(create_time__gte=cutoff_time).all()

        # 按状态分组统计
        status_distribution = {
            'pending': 0,
            'running': 0,
            'completed': 0,
            'failed': 0
        }

        processing_times = []
        completed_tasks = []
        failed_tasks = []

        for task in all_tasks:
            status_key = task.status.value.lower()
            if status_key in status_distribution:
                status_distribution[status_key] += 1

            # 收集处理时间数据
            if task.status == TaskStatus.COMPLETED and task.start_time and task.end_time:
                duration = (task.end_time - task.start_time).total_seconds()
                processing_times.append(duration)
                completed_tasks.append({
                    'id': task.id,
                    'name': task.name,
                    'duration': duration,
                    'start_time': task.start_time.isoformat(),
                    'end_time': task.end_time.isoformat(),
                    'file_count': len(task.file_list) if task.file_list else 0
                })
            elif task.status == TaskStatus.FAILED:
                failed_tasks.append({
                    'id': task.id,
                    'name': task.name,
                    'error_message': task.error_message or '未知错误',
                    'create_time': task.create_time.isoformat(),
                    'file_count': len(task.file_list) if task.file_list else 0
                })

        # 计算处理时间统计
        processing_stats = {
            'avg_duration': sum(processing_times) / len(processing_times) if processing_times else 0,
            'min_duration': min(processing_times) if processing_times else 0,
            'max_duration': max(processing_times) if processing_times else 0,
            'median_duration': sorted(processing_times)[len(processing_times)//2] if processing_times else 0
        }

        # 生成处理速度趋势（按小时统计）
        trend_data = []
        for i in range(min(hours, 24)):  # 最多显示24小时的趋势
            hour_start = cutoff_time + timedelta(hours=i)
            hour_end = hour_start + timedelta(hours=1)

            hour_tasks = await StrmTask.filter(
                create_time__gte=hour_start,
                create_time__lt=hour_end
            ).all()

            hour_completed = len([t for t in hour_tasks if t.status == TaskStatus.COMPLETED])
            hour_failed = len([t for t in hour_tasks if t.status == TaskStatus.FAILED])
            hour_total = len(hour_tasks)

            # 计算该小时的平均处理时间
            hour_processing_times = []
            for task in hour_tasks:
                if task.status == TaskStatus.COMPLETED and task.start_time and task.end_time:
                    duration = (task.end_time - task.start_time).total_seconds()
                    hour_processing_times.append(duration)

            trend_data.append({
                'timestamp': hour_start.isoformat(),
                'total_tasks': hour_total,
                'completed_tasks': hour_completed,
                'failed_tasks': hour_failed,
                'success_rate': (hour_completed / hour_total) if hour_total > 0 else 0,
                'avg_processing_time': sum(hour_processing_times) / len(hour_processing_times) if hour_processing_times else 0
            })

        # 获取当前队列状态
        current_running = await StrmTask.filter(status=TaskStatus.RUNNING).count()
        current_pending = await StrmTask.filter(status=TaskStatus.PENDING).count()

        # 最快和最慢的任务
        fastest_task = min(completed_tasks, key=lambda x: x['duration']) if completed_tasks else None
        slowest_task = max(completed_tasks, key=lambda x: x['duration']) if completed_tasks else None

        return {
            "status_distribution": status_distribution,
            "processing_stats": processing_stats,
            "queue_status": {
                "current_running": current_running,
                "current_pending": current_pending,
                "queue_length": current_running + current_pending
            },
            "performance_insights": {
                "fastest_task": fastest_task,
                "slowest_task": slowest_task,
                "total_completed": len(completed_tasks),
                "total_failed": len(failed_tasks),
                "success_rate": len(completed_tasks) / len(all_tasks) if all_tasks else 0
            },
            "trend_data": trend_data,
            "recent_failures": failed_tasks[-5:] if failed_tasks else [],  # 最近5个失败任务
            "analysis_period_hours": hours,
            "total_tasks_analyzed": len(all_tasks)
        }

    return Success(data=await MonitorCache.get_or_set(
        f"{CacheKeys.STRM_TASK_STATS}:{hours}h",
        fetch_strm_task_stats,
        ttl_seconds=300  # 5分钟缓存
    ))


@router_monitor.get("/aggregation-status", summary="获取数据聚合状态")
async def get_aggregation_status(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
):
    """获取数据聚合状态和调度器信息"""
    try:
        # 获取聚合数据概览
        aggregation_summary = await DataAggregator.get_aggregation_summary()

        # 获取调度器状态
        scheduler_status = get_scheduler_status()

        data = {
            "aggregation_summary": aggregation_summary,
            "scheduler_status": scheduler_status,
            "timestamp": datetime.now().isoformat()
        }

        return Success(data=data)

    except Exception as e:
        return Success(data={"error": f"获取聚合状态失败: {str(e)}"})


@router_monitor.get("/historical-data", summary="获取历史聚合数据")
async def get_historical_data(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
    data_type: str = Query("hourly", description="数据类型: hourly 或 daily"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    limit: int = Query(100, description="返回记录数限制")
):
    """获取历史聚合数据"""
    try:
        if data_type == "hourly":
            query = MonitorHourlyStats.all()
            if start_date:
                query = query.filter(date_hour__gte=datetime.fromisoformat(start_date))
            if end_date:
                query = query.filter(date_hour__lte=datetime.fromisoformat(end_date))

            data = await query.order_by('-date_hour').limit(limit).values()

        elif data_type == "daily":
            query = MonitorDailyStats.all()
            if start_date:
                query = query.filter(date__gte=datetime.fromisoformat(start_date).date())
            if end_date:
                query = query.filter(date__lte=datetime.fromisoformat(end_date).date())

            data = await query.order_by('-date').limit(limit).values()

        else:
            return Success(data={"error": "无效的数据类型，请使用 'hourly' 或 'daily'"})

        return Success(data={
            "data_type": data_type,
            "records": data,
            "count": len(data),
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        return Success(data={"error": f"获取历史数据失败: {str(e)}"})


@router_monitor.post("/trigger-aggregation", summary="手动触发数据聚合")
async def trigger_aggregation(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
    aggregation_type: str = Query("hourly", description="聚合类型: hourly 或 daily"),
    target_date: Optional[str] = Query(None, description="目标日期时间 (ISO格式)")
):
    """手动触发数据聚合任务"""
    try:
        target_datetime = None
        if target_date:
            target_datetime = datetime.fromisoformat(target_date)

        if aggregation_type == "hourly":
            result = await DataAggregator.aggregate_hourly_data(target_datetime)
        elif aggregation_type == "daily":
            result = await DataAggregator.aggregate_daily_data(target_datetime)
        else:
            return Success(data={"error": "无效的聚合类型，请使用 'hourly' 或 'daily'"})

        return Success(data={
            "aggregation_type": aggregation_type,
            "result": result,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        return Success(data={"error": f"触发聚合失败: {str(e)}"})


@router_monitor.post("/cleanup-old-data", summary="清理过期数据")
async def cleanup_old_data(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
):
    """手动触发过期数据清理"""
    try:
        result = await DataAggregator.cleanup_old_data()

        return Success(data={
            "cleanup_result": result,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        return Success(data={"error": f"清理数据失败: {str(e)}"})


# 导出路由器
__all__ = ["router_monitor"]
